"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"

interface NewsItem {
  id: number
  documentId: string
  title: string
  newsbody: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  Classification: string
}

interface NewsResponse {
  data: NewsItem[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export default function NewsPage() {
  const [news, setNews] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const pageSize = 10

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true)
        const response = await fetch(
          `http://43.153.145.176:1337/api/news2?pagination[page]=${currentPage}&pagination[pageSize]=${pageSize}&sort=publishedAt:desc`
        )
        
        if (!response.ok) {
          throw new Error('Failed to fetch news')
        }
        
        const data: NewsResponse = await response.json()
        setNews(data.data)
        setTotalPages(data.meta.pagination.pageCount)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchNews()
  }, [currentPage])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '.')
  }

  const getClassificationLabel = (classification: string) => {
    return classification.replace(/^info/, '')
  }

  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="https://www.huariparts.co.jp/wp/wp-content/uploads/2022/05/news-top.jpg"
          alt="新着情報"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">新着情報</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-huari-red transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">新着情報</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-huari">
          <div className="max-w-4xl mx-auto">
            {loading ? (
              <div className="text-center py-12">
                <p className="text-gray-500">読み込み中...</p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-500">エラーが発生しました: {error}</p>
              </div>
            ) : news.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">お知らせはありません</p>
              </div>
            ) : (
              <>
                <div className="space-y-6">
                  {news.map((item) => (
                    <article key={item.id} className="border-b border-gray-200 pb-6">
                      <Link href={`/news/${item.documentId}`} className="block hover:text-huari-red transition-colors">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                          <p className="text-sm text-gray-500">
                            {formatDate(item.publishedAt)}
                            {item.Classification && (
                              <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded">
                                {getClassificationLabel(item.Classification)}
                              </span>
                            )}
                          </p>
                        </div>
                        <h2 className="text-lg font-medium mb-2">{item.title}</h2>
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {item.newsbody.substring(0, 150)}...
                        </p>
                      </Link>
                    </article>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <div className="flex space-x-2">
                      {currentPage > 1 && (
                        <button
                          onClick={() => setCurrentPage(currentPage - 1)}
                          className="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          前へ
                        </button>
                      )}
                      
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-4 py-2 border transition-colors ${
                            currentPage === page
                              ? 'bg-huari-red text-white border-huari-red'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                      
                      {currentPage < totalPages && (
                        <button
                          onClick={() => setCurrentPage(currentPage + 1)}
                          className="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          次へ
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Contact section - full width gray background like original */}
      <div className="bg-gray-100 py-12">
        <div className="container-huari text-center">
          <h3 className="text-2xl font-bold mb-2">CONTACT US</h3>
          <p className="text-lg font-bold mb-8">お見積り・ご相談はこちら</p>

          <div className="mb-2">
            <a href="tel:0723612111" className="text-2xl font-bold text-gray-800 hover:text-huari-red transition-colors">
              ************
            </a>
          </div>
          <p className="text-sm text-gray-600 mb-8">
            営業時間 9：00～17：00（土日祝 定休）
          </p>

          <Link
            href="/contact"
            className="inline-flex items-center gap-2 bg-huari-red text-white px-8 py-3 hover:bg-huari-red/90 transition-colors font-medium"
          >
            <Image
              src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/mail2.png"
              alt="お問い合わせ"
              width={24}
              height={24}
              className="h-auto"
            />
            お問い合わせ
          </Link>
        </div>
      </div>
    </div>
  )
}
