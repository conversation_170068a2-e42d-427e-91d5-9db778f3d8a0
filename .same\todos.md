# huari Parts Website Clone - Todos

## Setup and Structure
- [x] Initialize Next.js project with shadcn UI
- [x] Create basic file/folder structure
- [x] Set up global styles (colors, typography)
- [x] Create reusable layout components

## Components
- [x] Header with logo, navigation, and contact info
- [x] Hero section with background image
- [x] Services/Technologies section
- [x] Footer with links and company info
- [x] Navigation menu (desktop and mobile)
- [x] Card components for technology showcase
- [x] Contact section

## Pages
- [x] Homepage with hero, services highlights
- [x] Advantage page
- [x] Technology page
- [x] Services page
- [x] Contact page

## Functionality
- [ ] Responsive design
- [ ] Navigation menu toggle
- [ ] Language switching
- [ ] Parts search

## Deployment
- [x] Test site on different screen sizes
- [x] Final review and adjustments
- [x] Deploy site
