import Link from "next/link"
import Image from "next/image"

export function IndustrySection() {
  return (
    <div className="py-16 bg-white">
      <div className="container-huari">
        <h2 className="text-3xl font-bold text-center mb-2">様々な業界で</h2>
        <h3 className="text-xl font-bold text-huari-red text-center mb-6">モノづくりを<br className="sm:hidden" />支えています</h3>
        <p className="text-center text-gray-700 mb-10">
          あらゆる産業分野と密接した広く、そして深く企業を<br className="hidden sm:block" />
          サポートしています。<br />
          あなたの業界に合わせてご提案いたします。
        </p>

        <div className="flex flex-col md:flex-row items-center gap-8 mb-10">
          <div className="w-full md:w-1/2">
            <Link href="/scene" className="text-huari-red flex items-center font-bold mb-6 hover:underline">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><circle cx="12" cy="12" r="10"></circle><path d="m12 16 4-4-4-4"></path><path d="M8 12h8"></path></svg>
              さらなるご活用例（華日パーツ）
            </Link>
          </div>

          <div className="w-full md:w-1/2 flex justify-center">
            <Image
              src="https://ext.same-assets.com/2260909892/2180178137.png"
              alt="様々な業界での活用"
              width={400}
              height={400}
              className="rounded-full"
            />
          </div>
        </div>

        <div className="bg-[#f5f5f5] p-6 border border-gray-200 rounded-sm shadow-sm">
          <div className="flex items-center justify-center gap-2 mb-8">
            <h3 className="text-xl font-bold">鋳物のプロに聞く</h3>
            <Link href="/casting-navi" className="inline-block">
              <Image
                src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/top/casting_navi.png"
                alt="Casting Navi"
                width={150}
                height={40}
              />
            </Link>
          </div>

          <p className="text-center text-sm text-gray-700">
            PAMICとCastingNavi（ダイカスト専門サイト）では<br className="hidden sm:block" />
            PAMICクラブ会員+IDを付与して論文を始め特典を出しています。
          </p>
        </div>
      </div>
    </div>
  )
}
