import { Metadata } from "next"
import { Suspense } from "react"
import { ProductPageContent } from "./components/ProductPageContent"

export const metadata: Metadata = {
  title: "製作事例 | 寧波市北倫華日金属製品有限公司",
  description: "寧波市北倫華日金属製品有限公司の製作事例をご覧ください。様々な産業分野での部品製造実績をご紹介します。",
}

function ProductPageLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative w-full h-[300px] overflow-hidden bg-gray-200">
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">製作事例</h1>
          </div>
        </div>
      </div>
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <span className="text-gray-500">HOME</span>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">製作事例</span>
          </nav>
        </div>
      </div>
      <div className="py-12 bg-white">
        <div className="container-huari">
          <div className="text-center">読み込み中...</div>
        </div>
      </div>
    </div>
  )
}

export default function ProductPage() {
  return (
    <Suspense fallback={<ProductPageLoading />}>
      <ProductPageContent />
    </Suspense>
  )
}
