"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

export function PartsSearchHero() {
  const [category, setCategory] = useState("")
  const [keyword, setKeyword] = useState("")
  const [searchText, setSearchText] = useState("")
  const router = useRouter()

  // 实际的产品分类
  const categories = [
    "照明器具",
    "産業用油圧ドライブ",
    "産業用駆動装置",
    "自動化および通信電子機器",
    "自動車・オートバイ部品"
  ]

  // 常见的产品关键词（从实际产品名称中提取）
  const keywords = [
    "LED", "ランプ", "ライト", "シェル", "カバー", "ヒートシンク",
    "フィルター", "モーター", "ケース", "ドライブ", "リンクバー",
    "フランジ", "フレーム", "チェーンソー", "ベース",
    "ロボット", "サーボ", "クーラー", "ステー", "ボックス",
    "ミシン", "サーバー", "電気", "クリップ",
    "クラッチ", "トランスミッション", "オイルパン", "リアケース",
    "燃料", "電動", "エアコン", "分配", "ユニット", "ジョイント"
  ]

  const handleSearch = () => {
    // 构建搜索参数
    const searchParams = new URLSearchParams()

    if (category) searchParams.set('category', category)
    if (keyword) searchParams.set('keyword', keyword)
    if (searchText) searchParams.set('searchText', searchText)

    // 跳转到产品页面并传递搜索参数
    const url = searchParams.toString()
      ? `/product?${searchParams.toString()}`
      : '/product'

    router.push(url)
  }

  return (
    <div
      className="relative py-12 border-b border-gray-200 bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/parts_search2.png')"
      }}
    >
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-white/80"></div>

      <div className="container-huari relative z-10">
        {/* Parts Search Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center items-center mb-6">
            <Image
              src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/parts_search.png"
              alt="PARTS×SEARCH"
              width={250}
              height={75}
              className="h-auto"
            />
          </div>
          <p className="text-base text-gray-700 mb-2 font-medium">
            どの様な部品をお探しでしょうか？
          </p>
          <p className="text-base text-gray-700">
            分類やキーワードから、製品をお探しいただけます。
          </p>
        </div>

        {/* Search Form */}
        <div className="max-w-6xl mx-auto bg-white p-8 rounded-lg shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-end">
            {/* Category Selection */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                ▼[分類]を選択
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-huari-red focus:outline-none transition-colors"
              >
                <option value="">すべての分類</option>
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
              {category && (
                <button
                  onClick={() => setCategory("")}
                  className="mt-2 text-xs text-huari-red hover:text-huari-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Keyword Selection */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                ▼[キーワード]を選択
              </label>
              <select
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-huari-red focus:outline-none transition-colors"
              >
                <option value="">すべてのキーワード</option>
                {keywords.map((kw) => (
                  <option key={kw} value={kw}>
                    {kw}
                  </option>
                ))}
              </select>
              {keyword && (
                <button
                  onClick={() => setKeyword("")}
                  className="mt-2 text-xs text-huari-red hover:text-huari-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Free Text Search */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                フリーテキスト検索
              </label>
              <input
                type="text"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="製品名で検索..."
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-huari-red focus:outline-none transition-colors"
              />
              {searchText && (
                <button
                  onClick={() => setSearchText("")}
                  className="mt-2 text-xs text-huari-red hover:text-huari-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Search Button */}
            <div className="flex-shrink-0">
              <button
                onClick={handleSearch}
                className="bg-huari-red text-white px-12 py-4 rounded-sm hover:bg-huari-red/90 transition-colors font-bold text-lg shadow-lg"
              >
                検 索
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
