<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成公司文字图片</title>
    <style>
        body {
            font-family: 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', sans-serif;
            padding: 20px;
            background: #f0f0f0;
            text-align: center;
        }
        canvas {
            border: 2px solid #007cba;
            background: rgba(0,0,0,0.1);
            margin: 20px 0;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 15px 30px;
            font-size: 18px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
        }
        button:hover {
            background: #005a87;
        }
        .download-btn {
            background: #28a745;
        }
        .download-btn:hover {
            background: #1e7e34;
        }
        .text-preview {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 公司文字图片生成器</h1>
    
    <div class="info">
        <h3>📝 要生成的文字内容：</h3>
        <div class="text-preview">
            寧波市北倫華日金属製品有限公司は2002年に設立され、金型設計・製造、ダイカスト生産加工、精密機械加工、表面処理などを手がけるワンストップソリューションを提供するハイテク企業です。
        </div>
        
        <p><strong>图片规格：</strong> 700×140px | <strong>背景：</strong> 透明 | <strong>文字：</strong> 白色带阴影</p>
    </div>
    
    <button onclick="generateImage()">🎨 生成图片</button>
    <button class="download-btn" onclick="downloadImage()">📥 下载 company-text.png</button>
    
    <canvas id="textCanvas" width="700" height="140"></canvas>
    
    <div class="info">
        <h3>📋 使用说明：</h3>
        <ol style="text-align: left; display: inline-block;">
            <li>点击 "生成图片" 按钮创建图片</li>
            <li>点击 "下载 company-text.png" 按钮下载图片</li>
            <li>将下载的图片放入项目的 <code>public/images/hero/</code> 文件夹</li>
            <li>确保文件名为 <code>company-text.png</code></li>
        </ol>
    </div>
    
    <script>
        const canvas = document.getElementById('textCanvas');
        const ctx = canvas.getContext('2d');
        
        // 要显示的文字
        const text = '寧波市北倫華日金属製品有限公司は2002年に設立され、金型設計・製造、ダイカスト生産加工、精密機械加工、表面処理などを手がけるワンストップソリューションを提供するハイテク企業です。';
        
        function generateImage() {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体样式 - 使用更好的日文字体
            ctx.font = 'bold 20px "Noto Sans JP", "Hiragino Kaku Gothic ProN", "Hiragino Sans", "Yu Gothic", "Meiryo", sans-serif';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 添加文字阴影效果，增强可读性
            ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.shadowBlur = 6;
            
            // 将长文本分行显示
            const maxWidth = 650;
            const lineHeight = 28;
            const lines = wrapText(ctx, text, maxWidth);
            
            // 计算起始Y位置，使文本垂直居中
            const totalHeight = lines.length * lineHeight;
            const startY = (canvas.height - totalHeight) / 2 + lineHeight / 2;
            
            // 绘制每一行文字
            lines.forEach((line, index) => {
                const y = startY + index * lineHeight;
                ctx.fillText(line, canvas.width / 2, y);
            });
            
            console.log('✅ 图片生成完成！');
        }
        
        function wrapText(context, text, maxWidth) {
            const words = text.split('');
            const lines = [];
            let currentLine = '';
            
            for (let i = 0; i < words.length; i++) {
                const testLine = currentLine + words[i];
                const metrics = context.measureText(testLine);
                const testWidth = metrics.width;
                
                if (testWidth > maxWidth && currentLine !== '') {
                    lines.push(currentLine);
                    currentLine = words[i];
                } else {
                    currentLine = testLine;
                }
            }
            lines.push(currentLine);
            return lines;
        }
        
        function downloadImage() {
            // 检查是否已生成图片
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const hasContent = imageData.data.some(pixel => pixel !== 0);
            
            if (!hasContent) {
                alert('请先点击 "生成图片" 按钮！');
                return;
            }
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = 'company-text.png';
            link.href = canvas.toDataURL('image/png');
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('📥 图片下载完成：company-text.png');
            alert('✅ 图片已下载！\n请将文件放入 public/images/hero/ 文件夹中');
        }
        
        // 页面加载时自动生成图片
        window.onload = function() {
            generateImage();
        };
    </script>
</body>
</html>
