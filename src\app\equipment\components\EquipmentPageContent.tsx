"use client"

import Image from "next/image"
import { useState, useEffect } from "react"

interface EquipmentImage {
  id: string
  src: string
  alt: string
}

export function EquipmentPageContent() {
  const [equipmentImages, setEquipmentImages] = useState<EquipmentImage[]>([])

  useEffect(() => {
    // Load equipment images from the eqiupment directory
    const images: EquipmentImage[] = [
      { id: "e1", src: "/images/eqiupment/e1.jpg", alt: "設備 1" },
      { id: "e2", src: "/images/eqiupment/e2.jpg", alt: "設備 2" },
      { id: "e3", src: "/images/eqiupment/e3.jpg", alt: "設備 3" },
      { id: "e4", src: "/images/eqiupment/e4.jpg", alt: "設備 4" },
      { id: "e5", src: "/images/eqiupment/e5.jpg", alt: "設備 5" },
      { id: "e6", src: "/images/eqiupment/e6.jpg", alt: "設備 6" },
      { id: "e7", src: "/images/eqiupment/e7.png", alt: "CNC精密加工センター 1" },
      { id: "e8", src: "/images/eqiupment/e8.png", alt: "CNC精密加工センター 2" },
    ]
    setEquipmentImages(images)
  }, [])

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="bg-gray-100 py-16">
        <div className="container-huari">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              既存の設備
            </h1>
            <p className="text-gray-600 max-w-4xl mx-auto leading-relaxed">
              工場には280トン、500トン、800トン、1000トンおよび1600トンのダイカストマシンを備えており、自動アーム装置と真空圧鑄設備、ならびに集中溶解炉を完備し、お客様に高品質・高効率なトータルソリューションを提供しています。
            </p>
            <p className="text-gray-600 max-w-4xl mx-auto leading-relaxed mt-4">
              また、当社には3軸および4軸CNC精密加工センター、CNC旋盤機械が16台以上用意されており、洗浄設備も整っているため、ダイカスト製品における多くのユーザーの精密機械加工ニーズに対応することが可能です。
            </p>
          </div>
        </div>
      </div>

      {/* Equipment Images Grid Section */}
      <div className="py-16 bg-white">
        <div className="container-huari">
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
              設備一覧
            </h2>
          </div>

          {/* Equipment Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {equipmentImages.map((equipment) => (
              <div
                key={equipment.id}
                className="group relative bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow overflow-hidden"
              >
                <div className="relative aspect-[4/3] overflow-hidden">
                  <Image
                    src={equipment.src}
                    alt={equipment.alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-lg text-gray-800">
                    {equipment.alt}
                  </h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
