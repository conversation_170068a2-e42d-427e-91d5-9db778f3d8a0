"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Phone, Mail, User } from "lucide-react"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <header className="w-full fixed top-0 left-0 right-0 z-50">
      {/* Top thin bar with phone number */}
      <div className="bg-huari-red py-1">
        <div className="container-huari flex justify-end items-center">
          <div className="flex items-center gap-4 text-white text-xs">
            <span>人気の部品</span>
            <span>パイプ加工</span>
            <span>企業情報</span>
            <a href="tel:0723612111" className="flex items-center gap-1 font-bold">
              <Phone size={12} />
              ************
            </a>
          </div>
        </div>
      </div>

      {/* Main header with single-row layout */}
      <div className="py-2 bg-white shadow-sm">
        <div className="container-huari flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <Image
              src="/images/logos/hr-logo.png"
              alt="寧波市北倫華日金属製品有限公司"
              width={70}
              height={20}
              priority
              className="h-auto"
            />
          </Link>

          {/* Desktop Navigation Menu - positioned next to logo */}
          <nav className="hidden lg:flex items-center gap-6 ml-8">
            <div className="group relative">
              <Link href="/advantage" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
                華日の強み
              </Link>
            </div>

            <Link href="/manufacturer_function" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
              製造力
            </Link>

            <Link href="/product" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
              製作事例
            </Link>

            <Link href="/diecast-navi" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
              ダイカスト
            </Link>

            <Link href="/equipment" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
              既存の設備
            </Link>

            <Link href="/technology" className="text-sm font-medium text-gray-700 hover:text-huari-red transition-colors py-2">
              製造技術
            </Link>
          </nav>

          {/* Right side action buttons */}
          <div className="flex items-center gap-2">
            {/* Desktop action buttons */}
            <div className="hidden lg:flex items-center gap-2">
              <Link href="/recruit" className="flex items-center">
                <Image
                  src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/rec.png"
                  alt="採用情報"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
              <Link href="/contact" className="flex items-center">
                <Image
                  src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/inq.png"
                  alt="お問い合わせ"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden flex items-center gap-2">
              <Link href="/contact" className="flex items-center">
                <Image
                  src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/inq.png"
                  alt="お問い合わせ"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
              <button
                onClick={() => setIsOpen(true)}
                className="p-2"
              >
                <Image
                  src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/menu.png"
                  alt="メニューを開く"
                  width={30}
                  height={30}
                  className="hover:opacity-80 transition-opacity"
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent side="right" className="w-[80vw] sm:w-[385px] bg-white">
          <div className="flex justify-between items-center mb-6">
            <Link href="/" onClick={() => setIsOpen(false)}>
              <Image
                src="/images/logos/hr-logo.png"
                alt="寧波市北倫華日金属製品有限公司"
                width={70}
                height={20}
                className="h-auto"
              />
            </Link>
            <button onClick={() => setIsOpen(false)}>
              <Image
                src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/common/close.png"
                alt="メニューを閉じる"
                width={24}
                height={24}
              />
            </button>
          </div>

          <nav className="flex flex-col gap-4">
            <div className="border-b pb-3">
              <Link href="/advantage" className="block py-2 font-bold text-base" onClick={() => setIsOpen(false)}>
                華日の強み
              </Link>
            </div>

            <Link href="/manufacturer_function" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              製造力
            </Link>

            <Link href="/product" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              製作事例
            </Link>

            <Link href="/diecast-navi" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              ダイカスト
            </Link>

            <Link href="/equipment" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              既存の設備
            </Link>

            <Link href="/technology" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              製造技術
            </Link>

            <Link href="/contact" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              お問い合わせ
            </Link>

            <Link href="/company" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              企業情報
            </Link>

            <Link href="/recruit" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              採用情報
            </Link>

            <Link href="/faq" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              よくあるご質問
            </Link>

            <Link href="/news" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              お知らせ
            </Link>

            <Link href="/blog" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              ブログ
            </Link>

            <div className="pt-4">
              <Link href="/privacy" className="text-sm text-gray-600" onClick={() => setIsOpen(false)}>
                プライバシーポリシー
              </Link>
            </div>
          </nav>
        </SheetContent>
      </Sheet>
    </header>
  )
}
