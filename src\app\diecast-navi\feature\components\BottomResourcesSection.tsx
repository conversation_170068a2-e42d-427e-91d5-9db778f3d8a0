import Image from "next/image"
import Link from "next/link"

export function BottomResourcesSection() {
  return (
    <div className="bg-white">
      <div className="container-huari py-16">
        {/* Top Three Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          {/* FAQ Card */}
          <Link href="/diecast-navi/faq" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-3xl font-bold text-red-500 mb-4">FAQ</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              ダイカスト鋳造 コストダウンNaviに<br />
              寄せられるよくある質問
            </p>
          </Link>

          {/* 用語集 Card */}
          <Link href="/diecast-navi/glossary" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-3xl font-bold text-red-500 mb-4">用語集</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              ダイカスト鋳造 コストダウンNaviに<br />
              まつわるキーワード
            </p>
          </Link>

          {/* サービス提供の流れ Card */}
          <Link href="/diecast-navi/flow" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-2xl font-bold text-red-500 mb-4">サービス提供<br />の流れ</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              お問い合わせから<br />
              製品提供の流れ
            </p>
          </Link>
        </div>
      </div>

      {/* Contact Information with Pink Background */}
      <div
        className="relative py-16 bg-gradient-to-r from-pink-100 via-pink-50 to-pink-100 overflow-hidden"
        style={{
          backgroundImage: `
            linear-gradient(135deg, rgba(255, 182, 193, 0.3) 0%, rgba(255, 240, 245, 0.8) 50%, rgba(255, 182, 193, 0.3) 100%),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffb6c1' fill-opacity='0.1'%3E%3Cpolygon points='30,0 60,30 30,60 0,30'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
          `
        }}
      >
        {/* Geometric shapes */}
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-32 h-32 bg-pink-200 opacity-30 rotate-45"></div>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-32 h-32 bg-pink-200 opacity-30 rotate-45"></div>

        <div className="relative z-10 container-huari text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">お気軽にご相談ください</h2>
          <p className="text-gray-700 mb-8 leading-relaxed">
            ダイカスト鋳造 コストダウンNaviは、<br />
            特許製法であるダイカストカセットシステムを軸とした他社には真似できない強みにより、<br />
            皆様に高品質・コストダウン・製造リードタイムの短縮といったメリットを提供します。
          </p>

          {/* Large Phone Number */}
          <div className="mb-8">
            <div className="flex items-center justify-center mb-2">
              <span className="text-red-500 text-2xl mr-2">📞</span>
              <span className="text-4xl md:text-5xl font-bold text-gray-800">************</span>
            </div>
          </div>

          <Link
            href="/diecast-navi/contact"
            className="inline-block bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full font-bold text-lg transition-colors shadow-lg"
          >
            ご相談・お問い合わせはコチラ
          </Link>
        </div>
      </div>
    </div>
  )
}
