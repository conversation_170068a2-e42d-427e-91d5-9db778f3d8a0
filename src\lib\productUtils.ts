export interface Product {
  id: string
  name: string
  category: string
  imagePath: string
  fileName: string
}

export interface ProductCategory {
  name: string
  products: Product[]
}

// Static product data - in a real application, this would come from an API or database
export function getProductData(): ProductCategory[] {
  const categories: ProductCategory[] = [
    {
      name: '照明器具',
      products: [
        {
          id: 'lighting-led-street-lamp-shell',
          name: 'LEDストリートランプ用シェル',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDストリートランプ用シェル.png',
          fileName: 'LEDストリートランプ用シェル.png'
        },
        {
          id: 'lighting-led-spotlight',
          name: 'LEDスポットライト',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDスポットライト.png',
          fileName: 'LEDスポットライト.png'
        },
        {
          id: 'lighting-led-lamp-heatsink',
          name: 'LEDランプ用ヒートシンク',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDランプ用ヒートシンク.png',
          fileName: 'LEDランプ用ヒートシンク.png'
        },
        {
          id: 'lighting-led-factory-mine-shell',
          name: 'LED工場・鉱山灯用シェル',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LED工場・鉱山灯用シェル.png',
          fileName: 'LED工場・鉱山灯用シェル.png'
        },
        {
          id: 'lighting-garden-front-lamp-cover',
          name: 'ガーデンフロントランプカバー',
          category: '照明器具',
          imagePath: '/images/products/照明器具/ガーデンフロントランプカバー.png',
          fileName: 'ガーデンフロントランプカバー.png'
        }
      ]
    },
    {
      name: '産業用油圧ドライブ',
      products: [
        {
          id: 'hydraulic-bkm155-filter-main-shell',
          name: 'BKM155型フィルター用メインシェル',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/BKM155型フィルター用メインシェル.png',
          fileName: 'BKM155型フィルター用メインシェル.png'
        },
        {
          id: 'hydraulic-motor-link-bar',
          name: 'モーターリンクバー',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/モーターリンクバー.png',
          fileName: 'モーターリンクバー.png'
        },
        {
          id: 'hydraulic-expanded-motor-case',
          name: '増容型モーターケース',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/増容型モーターケース.png',
          fileName: '増容型モーターケース.png'
        },
        {
          id: 'hydraulic-drive-main-shell',
          name: '油圧ドライブ用メインシェル',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/油圧ドライブ用メインシェル.png',
          fileName: '油圧ドライブ用メインシェル.png'
        },
        {
          id: 'hydraulic-washer-flange-ring',
          name: '洗浄機用フランジリング',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/洗浄機用フランジリング.png',
          fileName: '洗浄機用フランジリング.png'
        },
        {
          id: 'hydraulic-mower-frame',
          name: '草刈機フレーム',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/草刈機フレーム.png',
          fileName: '草刈機フレーム.png'
        }
      ]
    },
    {
      name: '産業用駆動装置',
      products: [
        {
          id: 'drive-chainsaw-base',
          name: 'チェーンソー用ベース',
          category: '産業用駆動装置',
          imagePath: '/images/products/産業用駆動装置/チェーンソー用ベース.png',
          fileName: 'チェーンソー用ベース.png'
        },
        {
          id: 'drive-electric-motor-cover',
          name: '電動モーターカバー',
          category: '産業用駆動装置',
          imagePath: '/images/products/産業用駆動装置/電動モーターカバー.png',
          fileName: '電動モーターカバー.png'
        }
      ]
    },
    {
      name: '自動化および通信電子機器',
      products: [
        {
          id: 'automation-filter',
          name: 'フィルター',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/フィルター.png',
          fileName: 'フィルター.png'
        },
        {
          id: 'automation-robot-servo-cooler-stay',
          name: 'ロボット用サーボクーラーステー',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/ロボット用サーボクーラーステー.png',
          fileName: 'ロボット用サーボクーラーステー.png'
        },
        {
          id: 'automation-signal-distribution-box',
          name: '信号分配ボックス',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/信号分配ボックス.png',
          fileName: '信号分配ボックス.png'
        },
        {
          id: 'automation-industrial-sewing-heatsink-frame',
          name: '産業用ミシン用ヒートシンクフレーム',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/産業用ミシン用ヒートシンクフレーム.png',
          fileName: '産業用ミシン用ヒートシンクフレーム.png'
        },
        {
          id: 'automation-server-heatsink-stay',
          name: '自動制御サーバー用ヒートシンクステー',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/自動制御サーバー用ヒートシンクステー.png',
          fileName: '自動制御サーバー用ヒートシンクステー.png'
        },
        {
          id: 'automation-power-line-clip',
          name: '電力用電気ラインクリップ',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/電力用電気ラインクリップ.png',
          fileName: '電力用電気ラインクリップ.png'
        }
      ]
    },
    {
      name: '自動車・オートバイ部品',
      products: [
        {
          id: 'automotive-clutch-cooler',
          name: 'クラッチクーラー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/クラッチクーラー.png',
          fileName: 'クラッチクーラー.png'
        },
        {
          id: 'automotive-clutch-heatsink-cover',
          name: 'クラッチヒートシンクカバー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/クラッチヒートシンクカバー.png',
          fileName: 'クラッチヒートシンクカバー.png'
        },
        {
          id: 'automotive-transmission-oil-pan',
          name: 'トランスミッションオイルパン',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションオイルパン.png',
          fileName: 'トランスミッションオイルパン.png'
        },
        {
          id: 'automotive-transmission-rear-case',
          name: 'トランスミッションリアケース',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションリアケース.png',
          fileName: 'トランスミッションリアケース.png'
        },
        {
          id: 'automotive-transmission-rear-case-2',
          name: 'トランスミッションリアケース2',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションリアケース2.png',
          fileName: 'トランスミッションリアケース2.png'
        },
        {
          id: 'automotive-fuel-filter-double-case',
          name: '燃料フィルター二重電動ケース',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/燃料フィルター二重電動ケース.png',
          fileName: '燃料フィルター二重電動ケース.png'
        },
        {
          id: 'automotive-clutch-right-cover',
          name: '自動車クラッチ右側カバー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車クラッチ右側カバー.png',
          fileName: '自動車クラッチ右側カバー.png'
        },
        {
          id: 'automotive-ac-distribution-unit',
          name: '自動車用エアコン分配ユニット',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車用エアコン分配ユニット.png',
          fileName: '自動車用エアコン分配ユニット.png'
        },
        {
          id: 'automotive-joint',
          name: '自動車用ジョイント',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車用ジョイント.png',
          fileName: '自動車用ジョイント.png'
        }
      ]
    }
  ]

  return categories
}

/**
 * Get all products as a flat array
 */
export function getAllProducts(): Product[] {
  const categories = getProductData()
  return categories.flatMap(category => category.products)
}

/**
 * Get products by category
 */
export function getProductsByCategory(categoryName: string): Product[] {
  const categories = getProductData()
  const category = categories.find(cat => cat.name === categoryName)
  return category ? category.products : []
}

/**
 * Get unique categories
 */
export function getCategories(): string[] {
  const categories = getProductData()
  return categories.map(category => category.name)
}
