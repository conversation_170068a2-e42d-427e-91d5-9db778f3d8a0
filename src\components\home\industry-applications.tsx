import Link from "next/link"
import Image from "next/image"

export function IndustryApplications() {
  return (
    <div className="py-12 bg-gray-100">
      <div className="container-huari">
        {/* Company and Recruitment info cards - horizontal layout like original */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Link href="/company" className="group block">
            <div className="relative overflow-hidden rounded-lg">
              <Image
                src="images/industry/f1.jpg"
                alt="企業情報"
                width={553}
                height={323}
                className="w-full h-auto object-cover group-hover:opacity-80 transition-opacity"
                style={{
                  aspectRatio: '553/230',
                  objectPosition: 'center center'
                }}
              />
              {/* Text overlay in the center of the image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-4xl font-bold text-white drop-shadow-lg">企業情報</h3>
              </div>
            </div>
          </Link>

          <Link href="/recruit" className="group block">
            <div className="relative overflow-hidden rounded-lg">
              <Image
                src="https://www.huariparts.co.jp/wp/wp-content/themes/huariparts/images/top/recruit.png"
                alt="採用情報"
                width={553}
                height={323}
                className="w-full h-auto object-cover group-hover:opacity-80 transition-opacity"
                style={{
                  aspectRatio: '553/230',
                  objectPosition: 'center center'
                }}
              />
              {/* Text overlay in the center of the image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-4xl font-bold text-white drop-shadow-lg">採用情報</h3>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}
