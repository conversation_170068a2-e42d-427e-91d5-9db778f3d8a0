import type { Metadata } from "next"
import { Noto_Sans_JP } from "next/font/google"
import "./globals.css"
import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"

const notoSansJP = Noto_Sans_JP({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  display: "swap",
  variable: "--font-noto-sans-jp",
})

export const metadata: Metadata = {
  title: "寧波市北倫華日金属製品有限公司｜金型設計・製造、ダイカスト生産加工、精密機械加工、表面処理",
  description:
    "寧波市北倫華日金属製品有限公司は2002年に設立され、金型設計・製造、ダイカスト生産加工、精密機械加工、表面処理などを手がけるワンストップソリューションを提供するハイテク企業です。",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ja" className={notoSansJP.variable}>
      <body className="min-h-screen flex flex-col pt-[50px]">
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
